#!/usr/bin/env python3
"""
测试流式响应的脚本
"""
import requests
import json
import time

def test_streaming_response():
    """测试流式AI分析响应"""
    
    # 测试数据
    test_data = {
        "stock_codes": "000001\n000002",
        "keywords": "创新\n技术",
        "related_parties": "",
        "prompt": "请分析这些公司的创新情况",
        "openai_config": {}
    }
    
    print("🚀 开始测试流式响应...")
    print(f"📊 测试数据: {test_data}")
    
    try:
        # 发送流式请求
        response = requests.post(
            'http://localhost:5000/api/ai_analysis_stream',
            json=test_data,
            stream=True,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code != 200:
            print(f"❌ 请求失败: {response.status_code} - {response.text}")
            return
        
        print("✅ 连接成功，开始接收流式数据...")
        
        chunk_count = 0
        total_content = ""
        start_time = time.time()
        
        # 逐行读取流式响应
        for line in response.iter_lines(decode_unicode=True):
            if line:
                chunk_count += 1
                current_time = time.time() - start_time
                
                print(f"📦 [{current_time:.2f}s] 块 {chunk_count}: {line[:100]}...")
                
                if line.startswith('data: '):
                    try:
                        data = json.loads(line[6:])
                        
                        if data.get('type') == 'ai_chunk':
                            content = data.get('data', '')
                            total_content += content
                            print(f"   📝 AI内容: '{content}'")
                            
                        elif data.get('type') == 'status':
                            print(f"   📊 状态: {data.get('message')}")
                            
                        elif data.get('type') == 'contexts':
                            contexts = data.get('data', [])
                            print(f"   📋 上下文: {len(contexts)} 个")
                            
                        elif data.get('type') == 'complete':
                            print(f"   ✅ 完成: {data.get('message')}")
                            
                        elif data.get('type') == 'error':
                            print(f"   ❌ 错误: {data.get('message')}")
                            
                    except json.JSONDecodeError as e:
                        print(f"   ⚠️ JSON解析失败: {e}")
        
        end_time = time.time() - start_time
        print(f"\n📊 测试完成:")
        print(f"   ⏱️ 总耗时: {end_time:.2f}秒")
        print(f"   📦 总块数: {chunk_count}")
        print(f"   📝 总内容长度: {len(total_content)} 字符")
        print(f"   📈 平均速度: {chunk_count/end_time:.2f} 块/秒")
        
        if total_content:
            print(f"\n📄 AI分析结果预览:")
            print(f"{total_content[:200]}...")
        else:
            print("\n⚠️ 未收到AI分析内容")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_simple_request():
    """测试简单的非流式请求作为对比"""
    print("\n🔄 测试非流式请求作为对比...")
    
    try:
        response = requests.get('http://localhost:5000/api/health')
        if response.status_code == 200:
            print("✅ 服务器正常运行")
            print(f"📊 响应: {response.json()}")
        else:
            print(f"❌ 服务器异常: {response.status_code}")
    except Exception as e:
        print(f"❌ 连接失败: {e}")

if __name__ == "__main__":
    print("🧪 流式响应测试工具")
    print("=" * 50)
    
    # 先测试服务器连接
    test_simple_request()
    
    # 再测试流式响应
    test_streaming_response()
    
    print("\n✨ 测试完成")
