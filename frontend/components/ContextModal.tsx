'use client'

import { useState, useEffect } from 'react'
import { X, FileText, Search, ChevronLeft, ChevronRight } from 'lucide-react'
import { Button } from './ui/Button'
import { Badge } from './ui/Badge'
import { Loading } from './ui/Loading'
import { apiMethods } from '@/lib/api'
import { cn } from '@/lib/utils'
import toast from 'react-hot-toast'

interface ContextModalProps {
  isOpen: boolean
  onClose: () => void
  item: {
    analysis_id: string
    keyword: string
    company_name: string
    stock_code: string
    file_name: string
    count: number
  } | null
}

export function ContextModal({ isOpen, onClose, item }: ContextModalProps) {
  const [contextData, setContextData] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [contextLength, setContextLength] = useState(200)
  const itemsPerPage = 5

  useEffect(() => {
    if (isOpen && item) {
      fetchContext()
    }
  }, [isOpen, item, contextLength])

  const fetchContext = async () => {
    if (!item) return

    setLoading(true)
    try {
      console.log('🔍 获取上下文:', item)
      
      const response = await apiMethods.getKeywordContext({
        analysis_id: item.analysis_id,
        keyword: item.keyword,
        context_length: contextLength,
        stock_code_filter: item.stock_code,
        file_name_filter: item.file_name
      })

      console.log('📥 上下文响应:', response)

      if (response.data.success) {
        setContextData(response.data.data)
        setCurrentPage(1)
      } else {
        toast.error(response.data.message || '获取上下文失败')
      }
    } catch (error: any) {
      console.error('❌ 获取上下文失败:', error)
      toast.error('获取上下文失败: ' + (error.response?.data?.message || error.message))
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    setContextData(null)
    setCurrentPage(1)
    onClose()
  }

  if (!isOpen || !item) return null

  const contexts = contextData?.contexts || []
  const totalPages = Math.ceil(contexts.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentContexts = contexts.slice(startIndex, endIndex)

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      style={{ zIndex: 9999 }}
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          handleClose()
        }
      }}
    >
      <div
        className="bg-white rounded-lg shadow-2xl w-full max-w-5xl max-h-[90vh] overflow-hidden transform transition-all duration-200 scale-100"
        onClick={(e) => e.stopPropagation()}
      >
        {/* 头部 */}
        <div className="p-6 border-b border-gray-200 bg-gray-50">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-3 mb-3">
                <Search className="w-5 h-5 text-blue-600 flex-shrink-0" />
                <h3 className="text-lg font-semibold text-gray-900">
                  关键词上下文分析
                </h3>
              </div>
              <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                <span>关键词: <strong className="text-blue-600">"{item.keyword}"</strong></span>
                <span>公司: <strong>{item.company_name}</strong></span>
                <span>出现次数: <Badge variant="success">{item.count}</Badge></span>
              </div>
            </div>

            <div className="flex items-center space-x-3 ml-4">
              {/* 上下文长度选择 */}
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600 whitespace-nowrap">长度:</span>
                <select
                  value={contextLength}
                  onChange={(e) => setContextLength(Number(e.target.value))}
                  className="text-sm border border-gray-300 rounded px-2 py-1 bg-white"
                >
                  <option value={100}>100字符</option>
                  <option value={200}>200字符</option>
                  <option value={300}>300字符</option>
                  <option value={500}>500字符</option>
                </select>
              </div>

              <Button variant="ghost" size="sm" onClick={handleClose}>
                <X className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 overflow-y-auto" style={{ maxHeight: 'calc(90vh - 200px)' }}>
          <div className="p-6">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <Loading size="lg" text="正在获取上下文..." />
            </div>
          ) : contexts.length > 0 ? (
            <div className="space-y-4">
              {/* 统计信息 */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      <FileText className="w-4 h-4 text-blue-600" />
                      <span className="text-sm font-medium text-blue-900">
                        找到 {contexts.length} 个相关文件
                      </span>
                    </div>
                    <div className="text-sm text-blue-700">
                      总计 {contexts.reduce((sum: number, ctx: any) => sum + (ctx.total_count || 0), 0)} 处匹配
                    </div>
                  </div>
                  {totalPages > 1 && (
                    <div className="text-sm text-blue-700">
                      第 {currentPage} / {totalPages} 页
                    </div>
                  )}
                </div>
              </div>

              {/* 上下文列表 */}
              <div className="space-y-4">
                {currentContexts.map((context: any, index: number) => (
                  <div key={index} className="border border-gray-200 rounded-lg overflow-hidden">
                    <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <FileText className="w-4 h-4 text-gray-600" />
                          <span className="font-medium text-gray-900">
                            {context.file_name || '未知文件'}
                          </span>
                        </div>
                        <Badge variant="default" size="sm">
                          {context.total_count || 0} 处匹配
                        </Badge>
                      </div>
                    </div>
                    
                    <div className="p-4">
                      {context.snippets && context.snippets.length > 0 ? (
                        <div className="space-y-3">
                          {context.snippets.slice(0, 3).map((snippet: string, snippetIndex: number) => (
                            <div 
                              key={snippetIndex}
                              className="bg-yellow-50 border-l-4 border-yellow-400 p-3 rounded-r"
                            >
                              <div 
                                className="text-sm text-gray-700 leading-relaxed"
                                dangerouslySetInnerHTML={{ 
                                  __html: snippet.replace(
                                    new RegExp(`(${item.keyword})`, 'gi'),
                                    '<mark class="bg-yellow-200 px-1 rounded">$1</mark>'
                                  )
                                }}
                              />
                            </div>
                          ))}
                          {context.snippets.length > 3 && (
                            <div className="text-center">
                              <span className="text-xs text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                                还有 {context.snippets.length - 3} 个上下文片段...
                              </span>
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="text-center py-4 text-gray-500">
                          未找到具体的上下文片段
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {/* 分页 */}
              {totalPages > 1 && (
                <div className="flex items-center justify-center space-x-2 pt-4 border-t border-gray-200">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                  >
                    <ChevronLeft className="w-4 h-4" />
                    上一页
                  </Button>
                  
                  <div className="flex items-center space-x-1">
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      const page = i + 1
                      return (
                        <Button
                          key={page}
                          variant={currentPage === page ? "primary" : "ghost"}
                          size="sm"
                          onClick={() => setCurrentPage(page)}
                          className="w-8 h-8 p-0"
                        >
                          {page}
                        </Button>
                      )
                    })}
                  </div>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                  >
                    下一页
                    <ChevronRight className="w-4 h-4" />
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-12">
              <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                未找到相关上下文
              </h3>
              <p className="text-gray-500">
                关键词 "{item.keyword}" 在该文件中可能没有具体的上下文信息
              </p>
            </div>
          )}
          </div>
        </div>

        {/* 底部 */}
        <div className="flex items-center justify-between p-4 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-gray-600">
            文件: {item.file_name}
          </div>
          <Button variant="secondary" onClick={handleClose}>
            关闭
          </Button>
        </div>
      </div>
    </div>
  )
}
