# OpenAI API 配置
# 请在此处填入您的OpenAI API密钥
OPENAI_API_KEY=

# OpenAI API基础URL
OPENAI_BASE_URL=https://api.openai.com/v1

# OpenAI 模型名称
OPENAI_MODEL=gpt-3.5-turbo

# 数据库配置
DATABASE_PATH=data/database/cninfo.db

# 文件存储配置
TXT_DIR=txt
PDF_DIR=pdf
EXPORTS_DIR=exports
RESULTS_DIR=results

# Flask应用配置
FLASK_DEBUG=True
FLASK_PORT=5000
FLASK_HOST=0.0.0.0

# 日志配置
LOG_LEVEL=INFO

# 爬虫配置
REQUEST_TIMEOUT=30
REQUEST_RETRIES=3
REQUEST_DELAY=1

# AI分析配置
CONTEXT_LENGTH=300
MAX_CONTEXTS=10
AI_ANALYSIS_TIMEOUT=120
