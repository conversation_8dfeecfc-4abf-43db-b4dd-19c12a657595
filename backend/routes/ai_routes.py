"""
AI相关路由
"""
from flask import Blueprint, request, jsonify, Response
from services import AIService
from utils.response import success_response, error_response
from utils.validation import validate_required_fields

ai_bp = Blueprint('ai', __name__)

# 全局变量，在app.py中初始化
ai_service = None


def init_ai_routes(ai_svc):
    """初始化路由依赖"""
    global ai_service
    ai_service = ai_svc


@ai_bp.route('/ai_analysis', methods=['POST'])
def ai_analysis():
    """AI分析接口"""
    try:
        data = request.json
        
        # 验证必需字段
        required_fields = ['stock_codes', 'keywords', 'prompt']
        validation_error = validate_required_fields(data, required_fields)
        if validation_error:
            return error_response(validation_error)

        # 解析参数
        stock_codes = [code.strip() for code in data.get('stock_codes', '').split('\n') if code.strip()]
        keywords = [kw.strip() for kw in data.get('keywords', '').split('\n') if kw.strip()]
        related_parties = [rp.strip() for rp in data.get('related_parties', '').split('\n') if rp.strip()] if data.get('related_parties') else []
        prompt = data.get('prompt', '').strip()
        openai_config = data.get('openai_config', {})

        if not stock_codes:
            return error_response('请输入股票代码')

        if not keywords:
            return error_response('请输入统计关键词')

        if not prompt:
            return error_response('请输入AI分析要求')
        
        # 执行AI分析
        result = ai_service.ai_analysis(stock_codes, keywords, related_parties, prompt, openai_config)
        
        return jsonify(result)
        
    except Exception as e:
        return error_response(f'AI分析失败: {str(e)}')


@ai_bp.route('/ai_analysis_stream', methods=['POST'])
def ai_analysis_stream():
    """流式AI分析接口"""
    try:
        # 在请求上下文中完成所有数据处理
        data = request.get_json()
        if not data:
            return error_response('无效的请求数据')
        
        # 验证必需字段
        required_fields = ['stock_codes', 'keywords', 'prompt']
        validation_error = validate_required_fields(data, required_fields)
        if validation_error:
            return error_response(validation_error)

        # 解析参数
        stock_codes = [code.strip() for code in data.get('stock_codes', '').split('\n') if code.strip()]
        keywords = [kw.strip() for kw in data.get('keywords', '').split('\n') if kw.strip()]
        related_parties = [rp.strip() for rp in data.get('related_parties', '').split('\n') if rp.strip()] if data.get('related_parties') else []
        prompt = data.get('prompt', '').strip()
        openai_config = data.get('openai_config', {})

        # 参数验证
        if not stock_codes or not keywords or not prompt:
            return error_response('请填写必需的分析参数（股票代码、关键词、分析要求）')
        
        # 生成流式响应
        return Response(
            ai_service.ai_analysis_stream(stock_codes, keywords, related_parties, prompt, openai_config),
            mimetype='text/event-stream',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type',
                'Access-Control-Allow-Methods': 'POST'
            }
        )
        
    except Exception as e:
        return error_response(f'流式AI分析初始化失败: {str(e)}')


@ai_bp.route('/test_openai', methods=['POST'])
def test_openai():
    """测试OpenAI连接"""
    try:
        data = request.json
        use_default = data.get('use_default', False)
        
        if use_default:
            # 测试服务器默认配置
            if not ai_service.has_openai:
                return error_response('openai库未安装，请先安装：pip install openai')

            # 使用配置服务中的默认配置
            if not ai_service.default_api_key:
                return error_response('服务器未配置OpenAI API Key，请在.env文件中设置OPENAI_API_KEY')

            result = ai_service.test_openai_connection()
            
            if result['success']:
                return success_response({
                    'message': f"服务器默认配置连接成功！模型: {ai_service.default_model}, Base URL: {ai_service.default_base_url}, 响应: {result['response']}"
                })
            else:
                return error_response(f"服务器默认配置连接失败: {result['error']}")
        else:
            # 测试用户提供的配置
            api_key = data.get('api_key', '').strip()
            base_url = data.get('base_url', '').strip()
            model = data.get('model', 'gpt-3.5-turbo')
            
            if not api_key:
                return error_response('请提供API Key')
            
            if not base_url:
                return error_response('请提供Base URL')
            
            # 测试OpenAI连接
            result = ai_service.test_openai_connection(api_key, base_url, model)
            
            if result['success']:
                return success_response({
                    'message': f"连接成功！模型: {model}, 响应: {result['response']}"
                })
            else:
                return error_response(f"连接失败: {result['error']}")
                
    except Exception as e:
        return error_response(f'测试失败: {str(e)}')


@ai_bp.route('/test_openai_simple', methods=['POST'])
def test_openai_simple():
    """简单的OpenAI API测试"""
    try:
        data = request.json
        api_key = data.get('api_key', '')
        base_url = data.get('base_url', '')
        model = data.get('model', 'gpt-3.5-turbo')
        
        if not api_key:
            return error_response('没有可用的API Key')
        
        print(f"🧪 简单OpenAI测试: {base_url}, 模型: {model}")
        
        # 测试连接
        result = ai_service.test_openai_connection(api_key, base_url, model)
        
        if result['success']:
            return success_response({
                'message': 'API测试成功',
                'response': result['response']
            })
        else:
            return error_response(f"API测试失败: {result['error']}")
            
    except Exception as e:
        return error_response(f'测试异常: {str(e)}')


@ai_bp.route('/config', methods=['GET'])
def get_ai_config():
    """获取AI配置信息"""
    try:
        config_info = {
            'has_openai': ai_service.has_openai,
            'default_config': {
                'api_key_configured': bool(ai_service.default_api_key),
                'base_url': ai_service.default_base_url,
                'model': ai_service.default_model,
                'context_length': ai_service.context_length,
                'max_contexts': ai_service.max_contexts,
                'analysis_timeout': ai_service.analysis_timeout
            },
            'supported_models': [
                'gpt-3.5-turbo',
                'gpt-4',
                'gpt-4-turbo',
                'gpt-4o',
                'gpt-4o-mini'
            ]
        }

        return success_response(config_info)

    except Exception as e:
        return error_response(f'获取AI配置失败: {str(e)}')
